// Chakra UI version of Dashboard.js
import React, { useState, useEffect } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Heading,
  Text,
  Button,
  IconButton,
  Spinner,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Image,
  useToast,
  Stack,
  Badge,
} from "@chakra-ui/react";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  RepeatIcon,
} from "@chakra-ui/icons";
import { CalendarIcon } from "@chakra-ui/icons";
import { MdLocationOn, MdGroups } from "react-icons/md";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import "./CalendarStyle.css";
import {
  convertDateIntoIndianFormat,
  convertTime,
} from "../../components/helpers/dateHelper.js";
import moment from "moment-timezone";
import axios from "axios";
import { Select } from "@chakra-ui/react";

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const Dashboard = () => {
  const [selectedDates, setSelectedDates] = useState([new Date()]);
  const [mode] = useState("single");
  const [allBookings, setAllBookings] = useState([]);
  const [selectedBooking, setSelectedBooking] = useState({});
  const [loading, setLoading] = useState(true);
  const toast = useToast();

  const [coaches, setCoaches] = useState([]);
  const [selectedCoach, setSelectedCoach] = useState();

  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const handleDay = (action) => {
    const current = selectedDates[0];
    if (action === "prev") current.setDate(current.getDate() - 1);
    if (action === "next") current.setDate(current.getDate() + 1);
    if (action === "today") setSelectedDates([new Date()]);
    else setSelectedDates([new Date(current)]);
  };

  const handleDateChange = (date) => {
    setSelectedDates([date]);
  };

  const getLatestBookings = async () => {
    try {
      setLoading(true);

      const date = moment(selectedDates[0]).format("YYYY-MM-DD");

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/dashboard`,
        {
          date,
          coachId: selectedCoach,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = response.data;

      console.log(result, "coach result here");

      if (result?.responseCode === 0 && result?.status === "success") {
        setAllBookings(result.data);
        setSelectedBooking(result.data?.[0] || {});
      } else {
        toast({ title: "Failed to fetch coaches", status: "error" });
      }

      setLoading(false);
    } catch (err) {
      console.error(err);
      toast({ title: "Failed to load bookings", status: "error" });
      setLoading(false);
    }
  };

  const fetchCoachData = async () => {
    try {
      const response = await axios({
        method: "get",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach`,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setCoaches(response.data.data);

      console.log("Coach Data:", response);
      return response.data;
    } catch (error) {
      console.error("Error fetching coach data:", error);
      throw error;
    }
  };

  useEffect(() => {
    fetchCoachData();
  }, []);

  useEffect(() => {
    getLatestBookings();
  }, [selectedDates, selectedCoach]);

  return (
    <Layout title="Dashboard" content="container">
      <Box>
        <Flex px={12} py={5} justifyContent="space-between">
          <Flex align="center" gap={4}>
            <IconButton
              icon={<ChevronLeftIcon />}
              aria-label="Previous day"
              onClick={() => handleDay("prev")}
            />
            <IconButton
              icon={<ChevronRightIcon />}
              aria-label="Next day"
              onClick={() => handleDay("next")}
            />
            <Button onClick={() => handleDay("today")}>Today</Button>
            <Text fontWeight="semibold">
              {`${selectedDates[0].getDate()} ${
                months[selectedDates[0].getMonth()]
              } ${selectedDates[0].getFullYear()}`}
            </Text>
          </Flex>
        </Flex>

        <Flex direction={{ base: "column", md: "row" }} px={12} gap={6}>
          <Box flex="1" bg="white" p={6} borderRadius="md" boxShadow="md">
            <Calendar
              value={selectedDates}
              onChange={handleDateChange}
              tileClassName={() => "chakra-calendar-tile"}
            />
            <Flex
              direction="row"
              align="center"
              justify="space-between"
              mt={8}
              mb={4}
            >
              <Text fontSize="lg" fontWeight="bold">
                Upcoming
              </Text>
              <Select
                placeholder="Select option"
                maxW="200px"
                onChange={(x) => setSelectedCoach(x.target.value)}
              >
                {coaches &&
                  coaches.length > 0 &&
                  coaches.map((x) => {
                    return <option value={x._id}>{x.firstName}</option>;
                  })}
              </Select>
            </Flex>

            <Box maxH="52vh" overflowY="auto">
              {allBookings.length > 0 ? (
                <Box>
                  {allBookings.map((booking, idx) => (
                    <Box
                      key={idx}
                      borderWidth="1px"
                      borderRadius="md"
                      overflow="hidden"
                      mb={3}
                      onClick={() => setSelectedBooking(booking)}
                      bg={booking === selectedBooking ? "blue.50" : "white"}
                      p={4}
                      cursor="pointer"
                      _hover={{ bg: "gray.50" }}
                    >
                      <Box flex="1">
                        <Text fontWeight="medium">
                          {booking.name.length > 50
                            ? `${booking.name.slice(0, 50)}...`
                            : booking.name}
                        </Text>
                        <Flex
                          mt={1}
                          fontSize="sm"
                          color="gray.500"
                          align="center"
                          gap={2}
                        >
                          <CalendarIcon />
                          <Text>
                            {convertDateIntoIndianFormat(selectedDates[0])}
                            &nbsp; at &nbsp;
                            {`${convertTime(
                              booking?.startTime
                            )} - ${convertTime(booking?.endTime)}`}
                          </Text>
                        </Flex>
                      </Box>
                      <Flex justify="flex-end" mt={2} gap={2}>
                        {booking.type === "class" && (
                          <Badge colorScheme="green">Class</Badge>
                        )}
                        {booking.type === "course" && (
                          <Badge colorScheme="yellow">Course</Badge>
                        )}
                        {booking.type === "event" && (
                          <Badge colorScheme="blue">Break</Badge>
                        )}
                      </Flex>
                    </Box>
                  ))}
                </Box>
              ) : (
                <Text>No Bookings Found</Text>
              )}
            </Box>
          </Box>

          <Box flex="1" bg="white" p={6} borderRadius="md" boxShadow="sm">
            {loading ? (
              <Flex justify="center" py={10}>
                <Spinner size="lg" color="green.500" />
              </Flex>
            ) : selectedBooking.name ? (
              <Stack spacing={4}>
                <Flex justify="space-between" align="flex-start">
                  {/* Left - Image + Info */}
                  <Flex gap={4}>
                    <Image
                      boxSize="70px"
                      objectFit="cover"
                      borderRadius="md"
                      src={selectedBooking.image}
                      alt="Course"
                    />
                    <Box>
                      <Text fontWeight="semibold" fontSize="md">
                        {selectedBooking.name}
                      </Text>
                      <Text fontWeight="medium" color="blue.600" fontSize="sm">
                        {convertTime(selectedBooking.startTime)} -{" "}
                        {convertTime(selectedBooking.endTime)}
                      </Text>
                      <Flex
                        align="center"
                        color="gray.600"
                        fontSize="sm"
                        gap={1}
                        mt={1}
                      >
                        <MdLocationOn />
                        <Text>{selectedBooking.facility}</Text>
                      </Flex>
                    </Box>
                  </Flex>

                  {/* Right - Date, Count, Refresh */}
                  <Box textAlign="right" fontSize="sm">
                    <Button
                      size="sm"
                      variant="ghost"
                      colorScheme="green"
                      leftIcon={<RepeatIcon />}
                      onClick={getLatestBookings}
                    >
                      Refresh
                    </Button>
                    <Text mt={1}>
                      {convertDateIntoIndianFormat(selectedDates[0])}
                    </Text>
                    <Text color="gray.500" mt={1}>
                      No. of Bookings: {selectedBooking?.bookings?.length || 0}
                    </Text>
                  </Box>
                </Flex>

                {/* No bookings message */}
                <Box
                  borderTop="1px solid"
                  borderColor="gray.100"
                  pt={3}
                  textAlign="left"
                  fontSize="sm"
                  color="gray.600"
                >
                  No Bookings Found
                </Box>
              </Stack>
            ) : (
              <Text>No Booking Selected</Text>
            )}
          </Box>
        </Flex>
      </Box>
    </Layout>
  );
};

export default Dashboard;
