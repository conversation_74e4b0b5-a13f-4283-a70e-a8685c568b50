import { useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  Flex,
  <PERSON>ing,
  Text,
  Button,
  IconButton,
  Badge,
  SimpleGrid,
  useToast,
} from "@chakra-ui/react";
import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";
import Calendar from "react-calendar";
import moment from "moment-timezone";
import "react-calendar/dist/Calendar.css";
import "../Dashboard/CalendarStyle.css";
import "./datePicker.css";

export default function CoachCalendar({ coachData }) {
  const toast = useToast();

  const [currentDate, setCurrentDate] = useState(new Date());
  const [allEvents, setAllEvents] = useState([]);

  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];

  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const formatDateToYYYYMMDD = (dateString) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2);
    const day = `0${date.getDate()}`.slice(-2);
    return `${year}-${month}-${day}`;
  };

  const handleDay = (action) => {
    const newDate = new Date(currentDate);
    if (action === "prev") newDate.setDate(newDate.getDate() - 1);
    if (action === "next") newDate.setDate(newDate.getDate() + 1);
    if (action === "today") newDate.setTime(Date.now());
    setCurrentDate(newDate);
  };

  const googleEvents = async () => {
    try {
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      headers.append("Authorization", `Bearer ${token}`);

      const data = {
        coachId: coachData._id,
        startDate: moment(currentDate).startOf("day"),
        endDate: moment(currentDate).endOf("day"),
      };

      const requestOptions = {
        method: "POST",
        headers,
        body: JSON.stringify(data),
      };

      const response = await fetch(
        `${process.env.REACT_APP_BASE_URL}/api/calendar/eventList`,
        requestOptions
      );
      const result = await response.json();

      if (result && !result.error) {
        const todayEvents = result.data?.filter(
          (x) =>
            formatDateToYYYYMMDD(currentDate) ===
            formatDateToYYYYMMDD(new Date(x.start.dateTime))
        );
        setAllEvents(todayEvents || []);
      } else {
        toast({
          title: "Failed to fetch events",
          description: result.message || "Something went wrong.",
          status: "error",
          duration: 4000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    googleEvents();
  }, [currentDate]);

  const positionedEvents = useMemo(() => {
    const sorted = [...allEvents].sort(
      (a, b) => new Date(a.start.dateTime) - new Date(b.start.dateTime)
    );
    const positioned = [];

    sorted.forEach((event) => {
      const startA = new Date(event.start.dateTime);
      const endA = new Date(event.end.dateTime);

      let colIndex = 0;
      let overlap = positioned.filter((e) => {
        const startB = new Date(e.start.dateTime);
        const endB = new Date(e.end.dateTime);
        return startA < endB && endA > startB;
      });

      while (overlap.find((e) => e.colIndex === colIndex)) {
        colIndex++;
      }

      positioned.push({
        ...event,
        colIndex,
        colTotal: Math.max(colIndex + 1, ...overlap.map((e) => e.colIndex + 1)),
      });
    });

    return positioned;
  }, [allEvents]);

  return (
    <Box h="100vh" display="flex" flexDirection="column" bg="gray.50">
      {/* Header */}
      <Box
        as="header"
        display="flex"
        flexDirection={{ base: "column", md: "row" }}
        alignItems="center"
        justifyContent={{ base: "center", md: "space-between" }}
        borderBottomWidth="1px"
        borderColor="gray.200"
        px={6}
        py={4}
        position="sticky"
        top={0}
        bg="white"
        boxShadow="sm"
        zIndex={1}
      >
        <Box textAlign="center">
          <Heading size="md" fontWeight="semibold">{`${currentDate.getDate()} ${
            months[currentDate.getMonth()]
          } ${currentDate.getFullYear()}`}</Heading>
          <Text color="gray.500" fontSize="sm">
            {daysOfWeek[currentDate.getDay()]}
          </Text>
        </Box>

        <SimpleGrid columns={5} spacing={2} mt={{ base: 4, md: 0 }}>
          <Badge colorScheme="red">Breaks</Badge>
          <Badge colorScheme="gray">Courses</Badge>
          <Badge colorScheme="green">Bookings</Badge>
          <Badge colorScheme="yellow">Sessions</Badge>
          <Badge colorScheme="blue">Other Events</Badge>
        </SimpleGrid>

        <Flex mt={{ base: 4, md: 0 }}>
          <IconButton
            icon={<ChevronLeftIcon />}
            onClick={() => handleDay("prev")}
            size="sm"
            mr={2}
          />
          <Button onClick={() => handleDay("today")} size="sm" variant="ghost">
            Today
          </Button>
          <IconButton
            icon={<ChevronRightIcon />}
            onClick={() => handleDay("next")}
            size="sm"
            ml={2}
          />
        </Flex>
      </Box>

      {/* Calendar Grid */}
      <Flex flex="1" overflow="hidden" position="relative">
        <Box flex="1" overflow="auto" bg="white">
          <Box position="relative" h="1440px">
            {/* Time Labels */}
            <Box
              position="absolute"
              left={0}
              top={0}
              bottom={0}
              w="60px"
              zIndex={1}
            >
              {[...Array(24)].map((_, i) => (
                <Box
                  key={i}
                  h="60px"
                  position="relative"
                  borderRightWidth="1px"
                  borderColor="gray.200"
                >
                  <Text
                    position="absolute"
                    right={2}
                    top={0}
                    fontSize="xs"
                    color="gray.500"
                  >
                    {i === 0
                      ? "12AM"
                      : i < 12
                      ? `${i}AM`
                      : i === 12
                      ? "12PM"
                      : `${i - 12}PM`}
                  </Text>
                </Box>
              ))}
            </Box>

            {/* Events Grid */}
            <Box
              position="absolute"
              left="60px"
              right={0}
              top={0}
              bottom={0}
              pl={2}
            >
              {[...Array(24)].map((_, i) => (
                <Box
                  key={`line-${i}`}
                  h="60px"
                  borderBottomWidth="1px"
                  borderColor="gray.100"
                />
              ))}

              {positionedEvents.map((event, index) => {
                const start = new Date(event.start.dateTime);
                const end = new Date(event.end.dateTime);

                const top = start.getHours() * 60 + start.getMinutes();
                const height = (end - start) / (1000 * 60); // in minutes

                const width = 100 / event.colTotal;
                const left = event.colIndex * width;

                return (
                  <Box
                    key={index}
                    position="absolute"
                    top={`${top}px`}
                    height={`${height}px`}
                    left={`${left}%`}
                    width={`${width}%`}
                    px={1}
                  >
                    <Box
                      bg="blue.50"
                      borderRadius="md"
                      p={2}
                      h="full"
                      borderLeftWidth="4px"
                      borderColor="blue.500"
                      boxShadow="sm"
                      display="flex"
                      flexDirection="column"
                      justifyContent="center"
                    >
                      <Text
                        fontWeight="medium"
                        fontSize="sm"
                        mb={1}
                        isTruncated
                      >
                        {event.summary}
                      </Text>
                      <Text fontSize="xs" color="gray.600">
                        {start
                          .toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true,
                          })
                          .toLowerCase()}{" "}
                        -{" "}
                        {end
                          .toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                            hour12: true,
                          })
                          .toLowerCase()}
                      </Text>
                    </Box>
                  </Box>
                );
              })}
            </Box>
          </Box>
        </Box>

        {/* Sidebar Calendar */}
        <Box
          display={{ base: "none", md: "block" }}
          w="96"
          borderLeftWidth="1px"
          borderColor="gray.200"
          p={6}
          bg="white"
          boxShadow="md"
        >
          <Calendar
            onChange={(value) => setCurrentDate(value)}
            value={currentDate}
            prev2Label={null}
            next2Label={null}
            className="custom-calendar"
          />
        </Box>
      </Flex>
    </Box>
  );
}
