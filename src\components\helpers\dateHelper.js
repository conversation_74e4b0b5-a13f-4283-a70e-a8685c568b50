const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

export function convertDateIntoIndianFormat(date) {
  const day = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();
  return `${day} ${month}, ${year}`;
}

export function convertTime(time) {
  const [hours, minutes] = time.split(":");
  let period = "AM";
  let hours12 = parseInt(hours, 10);

  if (hours12 >= 12) {
    period = "PM";
  }

  if (hours12 > 12) {
    hours12 -= 12;
  }

  if (hours12 === 0) {
    hours12 = 12;
  }

  return `${hours12}:${minutes.padStart(2, "0")} ${period}`;
}
